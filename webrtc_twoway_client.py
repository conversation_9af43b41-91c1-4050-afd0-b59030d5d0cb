import asyncio
import logging
import numpy as np
import random
import string
from aiortc import RTCPeerConnection, RTCConfiguration, RTCSessionDescription, RTCIceServer, RTCBundlePolicy
import socketio

import config
from config import (
    RTP_CAPABILITIES, VIDEO_WIDTH, VIDEO_HEIGHT
)


def generate_random_string(length):
    return ''.join(random.choices(string.ascii_letters + string.digits, k=length))


def generate_ssrc():
    return random.randint(100000000, 999999999)


def create_optimized_rtc_configuration(ice_servers_config):
    """
    Create optimized RTCConfiguration for faster ICE gathering and connection establishment.
    
    Args:
        ice_servers_config: ICE servers configuration from Fermax server response
        
    Returns:
        RTCConfiguration: Optimized configuration for WebRTC peer connections
    """
    # Convert Fermax ICE server config to aiortc RTCIceServer format
    ice_servers = []
    for server_config in ice_servers_config:
        # Handle both STUN and TURN servers
        for url in server_config.get("urls", []):
            ice_server = RTCIceServer(url)
            
            # Add credentials if provided (for TURN servers)
            if "credential" in server_config:
                ice_server.credential = server_config["credential"]
            if "username" in server_config:
                ice_server.username = server_config["username"]
            
            ice_servers.append(ice_server)
    
    # Add Google's public STUN servers as fallback for better connectivity
    fallback_stun = RTCIceServer("stun:stun.l.google.com:19302")
    ice_servers.append(fallback_stun)
    
    # Create optimized configuration
    config = RTCConfiguration(
        iceServers=ice_servers,
        bundlePolicy=RTCBundlePolicy.MAX_BUNDLE
    )
    
    return config


def setup_ice_gathering_monitoring(peer_connection, pc_name):
    """
    Setup ICE gathering state monitoring for a peer connection with timing metrics.
    
    Args:
        peer_connection: RTCPeerConnection instance to monitor
        pc_name: Human-readable name for logging (e.g., "video_pc", "audio_pc", "send_pc")
    """
    import time
    
    # Track timing for ICE gathering
    ice_gathering_start_time = None
    
    @peer_connection.on("icegatheringstatechange")
    async def on_ice_gathering_state_change():
        nonlocal ice_gathering_start_time
        
        state = peer_connection.iceGatheringState
        current_time = time.time()
        
        if state == "gathering":
            ice_gathering_start_time = current_time
            logging.info(f"🧊 ICE GATHERING [{pc_name}] - Started gathering ICE candidates")
        elif state == "complete":
            if ice_gathering_start_time:
                gathering_duration = current_time - ice_gathering_start_time
                logging.info(f"🧊 ICE GATHERING [{pc_name}] - Completed in {gathering_duration:.2f} seconds")
            else:
                logging.info(f"🧊 ICE GATHERING [{pc_name}] - Completed (duration unknown)")
        else:
            logging.debug(f"🧊 ICE GATHERING [{pc_name}] - State: {state}")
    
    @peer_connection.on("iceconnectionstatechange")
    async def on_ice_connection_state_change():
        state = peer_connection.iceConnectionState
        logging.info(f"🧊 ICE CONNECTION [{pc_name}] - State: {state}")
        
        if state == "connected":
            logging.info(f"✅ ICE CONNECTION [{pc_name}] - Successfully established")
        elif state == "failed":
            logging.error(f"❌ ICE CONNECTION [{pc_name}] - Failed to establish connection")
        elif state == "disconnected":
            logging.warning(f"⚠️ ICE CONNECTION [{pc_name}] - Connection lost")


def analyze_webrtc_differences(our_offer_sdp, server_answer_sdp, mobile_app_pickup_params=None):
    """
    Comprehensive analysis of WebRTC differences between our implementation and mobile app
    """
    logger = logging.getLogger(__name__)
    logging.info("🔍 WEBRTC ANALYSIS - Comprehensive Difference Analysis")
    logging.info("=" * 80)

    # 1. SDP Offer Analysis
    logging.info("📋 1. SDP OFFER ANALYSIS:")
    our_audio_lines = [line for line in our_offer_sdp.split('\n') if 'audio' in line.lower() or line.startswith('a=') or line.startswith('m=audio')]
    logging.info("🔍 Our audio SDP lines:")
    for line in our_audio_lines:
        if line.strip():
            logging.info(f"🔍   {line}")

    # 2. Server Response Analysis
    logging.info("\n📋 2. SERVER RESPONSE ANALYSIS:")
    if 'a=sendonly' in server_answer_sdp:
        logging.error("🚨 CRITICAL: Server rejected our audio transmission (a=sendonly)")
        logging.error("🚨 This means server will only SEND audio to us, not RECEIVE from us")
    elif 'a=recvonly' in server_answer_sdp:
        logging.info("✅ Server accepts our audio transmission (a=recvonly)")
    elif 'a=sendrecv' in server_answer_sdp:
        logging.info("✅ Server accepts bidirectional audio (a=sendrecv)")

    # 3. Mobile App Comparison
    if mobile_app_pickup_params:
        logging.info("\n📋 3. MOBILE APP COMPARISON:")
        logging.info("🔍 Mobile app pickup parameters:")
        if 'headerExtensions' in mobile_app_pickup_params:
            for ext in mobile_app_pickup_params['headerExtensions']:
                logging.info(f"🔍   {ext}")

    # 4. Potential Issues
    logging.info("\n📋 4. POTENTIAL ISSUES TO INVESTIGATE:")
    issues = []

    if 'a=sendonly' in server_answer_sdp:
        issues.append("Server rejecting audio transmission - check codec compatibility")
        issues.append("Server rejecting audio transmission - check header extensions")
        issues.append("Server rejecting audio transmission - check DTLS parameters")
        issues.append("Server rejecting audio transmission - check track configuration")
        issues.append("Server rejecting audio transmission - check transceiver direction")

    if not any('PCMA/8000' in line for line in our_audio_lines):
        issues.append("PCMA codec not found in our SDP offer")

    for i, issue in enumerate(issues, 1):
        logging.warning(f"⚠️  Issue {i}: {issue}")

    logging.info("=" * 80)


def convert_audio_frame_to_pcm(frame):
    """
    Convert WebRTC audio frame to raw PCM data for FFmpeg.
    WebRTC provides PCMA (G.711 A-law) which needs to be converted to linear PCM.
    """
    try:
        # Get audio data from the frame
        # The frame should contain PCMA encoded audio
        audio_array = frame.to_ndarray()

        # Convert from float32 to int16 for FFmpeg
        # Scale from [-1.0, 1.0] to [-32768, 32767]
        if audio_array.dtype == np.float32:
            # Clip values to prevent overflow
            audio_array = np.clip(audio_array, -1.0, 1.0)
            # Convert to int16
            audio_int16 = (audio_array * 32767).astype(np.int16)
        else:
            # If already int16, use as is
            audio_int16 = audio_array.astype(np.int16)

        # Return raw PCM bytes
        return audio_int16.tobytes()

    except Exception as e:
        logging.error(f"Error converting audio frame: {e}")
        return None


async def start_twoway_streaming_session():
    """
    Initiates a WebRTC session with the Fermax doorbell system that receives both video and audio,
    and transmits audio to the doorbell.

    This implements the enhanced three-phase protocol:
    Step 1: Establish video-only connection (like video_watch_only)
    Step 2: Send pickup event to enable audio reception AND transmission
    Step 3: Setup dedicated audio transport for receiving audio
    Step 4: Setup send transport for transmitting audio

    IMPORTANT: This should be called shortly after a doorbell press event.
    The room must exist on the Fermax server - if you get a "Room do not exist" error,
    it means either:
    1. The timing is wrong (try calling this right after doorbell press)
    2. The room has expired (wait for a new doorbell press)
    3. The room ID is invalid
    """
    sio = socketio.AsyncClient(logger=True, engineio_logger=True, reconnection=False)

    # Peer connections for receiving video and audio, and sending audio
    pc = RTCPeerConnection()  # Video receive peer connection
    audio_pc = RTCPeerConnection()  # Audio receive peer connection

    # CRITICAL FIX: Configure send peer connection to only use PCMA codec
    # The server rejects offers with multiple codecs (like Opus + PCMA)
    # We need to match the server's expected codec exactly
    from aiortc.codecs import PCMA_CODEC
    send_pc = RTCPeerConnection()  # Audio send peer connection
    
    # Setup ICE gathering monitoring for all peer connections
    setup_ice_gathering_monitoring(pc, "video_pc")
    setup_ice_gathering_monitoring(audio_pc, "audio_pc") 
    setup_ice_gathering_monitoring(send_pc, "send_pc")
    
    # 🎯 STAGE 0: EARLY CNAME OVERRIDE (PREVENT AUTO-GENERATION)
    # Override CNAME immediately after peer connection creation to prevent auto-generation
    async def early_cname_override():
        """Override CNAME at peer connection creation time to prevent auto-generation"""
        try:
            if pickup_cname:  # Only if pickup_cname is already generated
                if hasattr(send_pc, '_RTCPeerConnection__cname'):
                    original_early_cname = send_pc._RTCPeerConnection__cname
                    send_pc._RTCPeerConnection__cname = pickup_cname
                    logging.info(f"🎯 EARLY OVERRIDE SUCCESS - PC CNAME at creation: {original_early_cname} → {pickup_cname}")
                    return True
        except Exception as e:
            logging.debug(f"🎯 EARLY OVERRIDE - Cannot override at creation time: {e}")
        return False
    
    # 🎯 CNAME VERIFICATION FUNCTION
    def verify_cname_override(context=""):
        """Verify that CNAME override is still in effect"""
        try:
            pc_cname = getattr(send_pc, '_RTCPeerConnection__cname', 'NOT_FOUND')
            
            if pc_cname == pickup_cname:
                logging.info(f"🎯 CNAME VERIFICATION SUCCESS {context} - PC CNAME: {pc_cname}")
                return True
            else:
                logging.warning(f"🎯 CNAME VERIFICATION FAILED {context} - PC CNAME: {pc_cname}, Expected: {pickup_cname}")
                return False
        except Exception as e:
            logging.error(f"🎯 CNAME VERIFICATION ERROR {context}: {e}")
            return False
    
    call_failed_event = asyncio.Event()
    track_tasks = []
    server_info = None

    # Store track objects for later processing
    video_track = None
    audio_track = None
    send_audio_track = None
    
    # Store pickup event RTP parameters for consistency
    pickup_ssrc = None
    pickup_cname = None
    
    @pc.on("track")
    async def on_track(track):
        nonlocal video_track, audio_track
        logging.info(f"Track received on video PC: kind={track.kind}, id={track.id}")
        if track.kind == "video":
            logging.info("Video track received - starting video processing immediately")
            video_track = track
            # Start video processing immediately when track is available
            await start_video_processing()
        elif track.kind == "audio":
            logging.info("Audio track received on video PC - storing for processing after transport setup")
            audio_track = track
        else:
            logging.warning(f"Unknown track kind received: {track.kind}")
    
    @audio_pc.on("track")
    async def on_audio_track(track):
        nonlocal audio_track
        logging.info(f"Track received on audio PC: kind={track.kind}, id={track.id}")
        if track.kind == "audio":
            logging.info("Audio track received on audio PC - starting audio processing immediately")
            audio_track = track
            # Start audio processing immediately when track is available
            await start_audio_processing()
        else:
            logging.warning(f"Non-audio track received on audio PC: {track.kind}")

    @send_pc.on("connectionstatechange")
    async def on_send_connection_state_change():
        logging.info(f"Send PC connection state changed to: {send_pc.connectionState}")
        if send_pc.connectionState == "connected":
            logging.info("✅ Send transport connected - audio transmission should be active")
            # Check if we have transceivers and log their state
            if len(send_pc.getTransceivers()) > 0:
                for i, transceiver in enumerate(send_pc.getTransceivers()):
                    logging.info(f"Send transceiver {i}: kind={transceiver.kind}, direction={transceiver.direction}")
                    if transceiver.sender and transceiver.sender.track:
                        track = transceiver.sender.track
                        logging.info(f"Send transceiver {i} has track: {track.kind}")

                        # DIAGNOSTIC: Check track state (with error handling)
                        try:
                            logging.info(f"🔍 DIAGNOSTIC - Track state: readyState={getattr(track, 'readyState', 'unknown')}")
                            logging.info(f"🔍 DIAGNOSTIC - Track enabled: {getattr(track, 'enabled', 'unknown')}")
                            logging.info(f"🔍 DIAGNOSTIC - Track muted: {getattr(track, 'muted', 'unknown')}")
                            logging.info(f"🔍 DIAGNOSTIC - Track id: {getattr(track, 'id', 'unknown')}")

                            # DIAGNOSTIC: Check if it's our FileAudioTrack
                            if hasattr(track, '__class__'):
                                logging.info(f"🔍 DIAGNOSTIC - Track class: {track.__class__.__name__}")

                            # DIAGNOSTIC: Check sender state
                            sender = transceiver.sender
                            logging.info(f"🔍 DIAGNOSTIC - Sender transport: {sender.transport}")

                            # Start monitoring RTCRtpSender activity
                            asyncio.create_task(monitor_rtp_sender_activity(sender))

                        except Exception as e:
                            logging.error(f"🔍 DIAGNOSTIC - Error checking track state: {e}")
                            logging.info(f"🔍 DIAGNOSTIC - Track class: {track.__class__.__name__ if hasattr(track, '__class__') else 'unknown'}")

        elif send_pc.connectionState == "failed":
            logging.error("❌ Send transport connection failed")
        elif send_pc.connectionState == "disconnected":
            logging.warning("⚠️ Send transport disconnected")

    @send_pc.on("track")
    async def on_send_track(track):
        logging.info(f"Send PC track event: kind={track.kind}, id={track.id}")

    @send_pc.on("iceconnectionstatechange")
    async def on_send_ice_connection_state_change():
        logging.info(f"Send PC ICE connection state: {send_pc.iceConnectionState}")

    async def monitor_rtp_sender_activity(sender):
        """Monitor RTCRtpSender for outgoing packet activity and RTCP CNAME inspection"""
        try:
            logging.info("🔍 DIAGNOSTIC - Starting RTCRtpSender activity monitoring with RTCP inspection...")

            # Add RTCP packet monitoring to capture actual CNAME transmission
            async def inspect_rtcp_packets():
                """Inspect RTCP packets to find actual transmitted CNAME"""
                try:
                    # Try to access internal transport objects
                    if hasattr(sender, '_transport') and sender._transport:
                        transport = sender._transport
                        logging.info(f"📦 RTCP INSPECTION - Found sender transport: {transport}")
                        
                        # Look for RTCP-related properties
                        transport_attrs = [attr for attr in dir(transport) if 'rtcp' in attr.lower() or 'cname' in attr.lower()]
                        if transport_attrs:
                            logging.info(f"📦 RTCP INSPECTION - Transport RTCP attributes: {transport_attrs}")
                            for attr in transport_attrs:
                                try:
                                    value = getattr(transport, attr)
                                    logging.info(f"📦 RTCP INSPECTION - transport.{attr} = {value}")
                                except Exception as e:
                                    logging.debug(f"📦 RTCP INSPECTION - Cannot access transport.{attr}: {e}")
                    
                    # Try to access sender's internal RTP objects
                    if hasattr(sender, '_rtp') and sender._rtp:
                        rtp = sender._rtp
                        logging.info(f"📦 RTCP INSPECTION - Found sender._rtp: {rtp}")
                        
                        # Look for CNAME and RTCP-related properties
                        rtp_attrs = [attr for attr in dir(rtp) if 'rtcp' in attr.lower() or 'cname' in attr.lower() or 'ssrc' in attr.lower()]
                        if rtp_attrs:
                            logging.info(f"📦 RTCP INSPECTION - RTP RTCP attributes: {rtp_attrs}")
                            for attr in rtp_attrs:
                                try:
                                    value = getattr(rtp, attr)
                                    logging.info(f"📦 RTCP INSPECTION - rtp.{attr} = {value}")
                                except Exception as e:
                                    logging.debug(f"📦 RTCP INSPECTION - Cannot access rtp.{attr}: {e}")
                    
                    # Check sender itself for RTCP-related properties
                    sender_rtcp_attrs = [attr for attr in dir(sender) if 'rtcp' in attr.lower() or 'cname' in attr.lower() or 'ssrc' in attr.lower()]
                    if sender_rtcp_attrs:
                        logging.info(f"📦 RTCP INSPECTION - Sender RTCP attributes: {sender_rtcp_attrs}")
                        for attr in sender_rtcp_attrs:
                            try:
                                value = getattr(sender, attr)
                                logging.info(f"📦 RTCP INSPECTION - sender.{attr} = {value}")
                            except Exception as e:
                                logging.debug(f"📦 RTCP INSPECTION - Cannot access sender.{attr}: {e}")
                                
                except Exception as e:
                    logging.error(f"📦 RTCP INSPECTION - Error inspecting RTCP: {e}")

            # Run RTCP inspection immediately
            await inspect_rtcp_packets()

            # Monitor for 30 seconds
            for i in range(30):
                await asyncio.sleep(1)

                try:
                    # Get sender statistics
                    stats = await sender.getStats()

                    # Look for outbound RTP stream stats and RTCP information
                    for s in stats:
                        if "outbound-rtp" in s:
                            stat = stats[s]
                            packets_sent = getattr(stat, 'packetsSent', 0)
                            bytes_sent = getattr(stat, 'bytesSent', 0)
                            
                            # 📦 CRITICAL: Check for CNAME-related fields in stats
                            cname_fields = ['cname', 'rtcpCname', 'sdesCname', 'canonicalName']
                            found_cname = None
                            for field in cname_fields:
                                if hasattr(stat, field):
                                    found_cname = getattr(stat, field)
                                    logging.info(f"📦 CRITICAL DISCOVERY - Found CNAME in stats.{field}: {found_cname}")
                                    break
                            
                            if not found_cname:
                                # Check all attributes for CNAME-like values
                                all_attrs = [attr for attr in dir(stat) if not attr.startswith('_')]
                                for attr in all_attrs:
                                    try:
                                        value = getattr(stat, attr)
                                        if isinstance(value, str) and len(value) >= 8 and len(value) <= 32:
                                            # Could be a CNAME (typically 8-32 characters)
                                            logging.info(f"📦 POTENTIAL CNAME - stat.{attr}: {value}")
                                    except:
                                        pass
                            
                            logging.info(f"🔍 DIAGNOSTIC - RTCRtpSender stats: packets={packets_sent}, bytes={bytes_sent}")

                            if packets_sent > 0:
                                logging.info("✅ DIAGNOSTIC - RTCRtpSender IS sending packets!")
                                
                                # Run deeper RTCP inspection when packets are flowing
                                if i % 10 == 0:  # Every 10 seconds
                                    await inspect_rtcp_packets()
                                return

                    # If no outbound-rtp stats, log what we do have
                    if i % 5 == 0:  # Log every 5 seconds
                        logging.info(f"🔍 DIAGNOSTIC - RTCRtpSender monitoring ({i}s): No outbound-rtp stats yet")
                        for stat in stats:
                            logging.info(f"🔍 DIAGNOSTIC - Sender stat: {stat}")


                except Exception as e:
                    logging.info(f"🔍 DIAGNOSTIC - Error getting sender stats: {e}")

            logging.warning("⚠️ DIAGNOSTIC - RTCRtpSender monitoring complete: NO packets sent in 30 seconds")

        except Exception as e:
            logging.error(f"Error in RTCRtpSender monitoring: {e}")
    
    # Add connection state monitoring for main peer connection
    @pc.on("connectionstatechange")
    async def on_main_pc_connection_state_change():
        logging.info(f"Main PC connection state changed to: {pc.connectionState}")
        if pc.connectionState == "connected":
            logging.info("Main PC is now connected - video reception active")

    @pc.on("icecandidatepairchange")
    async def on_main_pc_ice_candidate_pair_change():
        logging.info(f"Main PC ICE candidate pair changed, state: {pc.iceConnectionState}")
        if pc.iceConnectionState == "connected":
            logging.info("Main PC ICE connection established - video reception active")
    

    
    async def start_video_processing():
        """Start processing video track immediately when available"""
        if not video_track:
            return
        
        logging.info("Starting video track processing immediately...")
        config.is_live.set()
        
        async def process_video_track():
            first_frame = True
            try:
                while True:
                    frame = await video_track.recv()
                    img = frame.to_image().resize((VIDEO_WIDTH, VIDEO_HEIGHT))
                    
                    if first_frame:
                        # Use custom save path if specified, otherwise default to last_call.png
                        save_path = config.custom_save_path if config.custom_save_path else "last_call.png"
                        img.save(save_path)
                        logging.info(f"Saved first frame to {save_path}")
                        
                        # Set appropriate completion flag
                        if config.custom_save_path:
                            config.snapshot_saved.set()  # Signal snapshot update complete
                        else:
                            config.frame_saved.set()  # Signal that frame has been saved
                        first_frame = False
                    
                    img_np = np.array(img, dtype=np.uint8)
                    img_bgr = img_np[:, :, ::-1]
                    await config.live_frame_queue.put(img_bgr.tobytes())
            except asyncio.CancelledError:
                logging.info("Video track processing cancelled")
                raise
            except Exception as e:
                logging.info(f"Live video track ended: {e}")
            finally:
                logging.info("Live video track finished. Reverting to dummy image.")
                config.is_live.clear()
                try:
                    await config.live_frame_queue.put(None)
                    while not config.live_frame_queue.empty():
                        try:
                            config.live_frame_queue.get_nowait()
                            config.live_frame_queue.task_done()
                        except:
                            break
                except Exception as e:
                    logging.warning(f"Error during video track cleanup: {e}")
        
        video_task = asyncio.create_task(process_video_track())
        track_tasks.append(video_task)

    async def start_audio_processing():
        """Start processing audio track immediately when available"""
        if not audio_track:
            return
        
        logging.info("Starting audio track processing immediately...")
        config.is_audio_live.set()
        
        async def process_audio_track():
            try:
                logging.debug("Audio track processing loop starting...")
                frame_count = 0
                while True:
                    try:
                        frame = await asyncio.wait_for(audio_track.recv(), timeout=10.0)
                        frame_count += 1
                    except asyncio.TimeoutError:
                        logging.debug(f"Timeout waiting for audio frame {frame_count}")
                        continue
                    # Removed frequent logging for performance
                    audio_data = convert_audio_frame_to_pcm(frame)
                    if audio_data:
                        await config.live_audio_queue.put(audio_data)
                        # Audio frame processed successfully
                    else:
                        logging.warning(f"Failed to convert audio frame to PCM")
            except asyncio.CancelledError:
                logging.info("Audio track processing cancelled")
                raise
            except Exception as e:
                logging.info(f"Live audio track ended: {e}")
            finally:
                logging.info("Live audio track finished. Reverting to dummy audio.")
                config.is_audio_live.clear()
                try:
                    await config.live_audio_queue.put(None)
                    while not config.live_audio_queue.empty():
                        try:
                            config.live_audio_queue.get_nowait()
                            config.live_audio_queue.task_done()
                        except:
                            break
                except Exception as e:
                    logging.warning(f"Error during audio track cleanup: {e}")
        
        audio_task = asyncio.create_task(process_audio_track())
        track_tasks.append(audio_task)

    @pc.on("connectionstatechange")
    async def on_connectionstatechange():
        logging.info(f"WebRTC Connection State is {pc.connectionState}")
        if pc.connectionState in ["failed", "closed", "disconnected"]:
            logging.error(f"WebRTC connection {pc.connectionState}.")
            # Cancel track processing tasks to ensure clean shutdown
            for task in track_tasks:
                if not task.done():
                    task.cancel()
            # Don't send None signal here - let track cleanup handle it
            call_failed_event.set()

    @sio.event
    async def connect():
        logging.info("Socket.IO connected. Joining call room for two-way audio...")
        
        # Log outgoing event in network capture format
        join_call_data = {
            "appToken": config.APP_TOKEN, 
            "roomId": config.ROOM_ID, 
            "fermaxOauthToken": config.FERMAX_OAUTH_TOKEN, 
            "protocolVersion": "0.8.2"
        }
        logging.debug(f"Sending join_call event")
        await sio.emit("join_call", join_call_data, callback=handle_join_response)

    @sio.event
    async def end_up(data):
        logging.debug(f"Received end_up event")
        logging.error(f"Call ended by server: {data.get('reason', 'unknown reason')}")
        call_failed_event.set()

    @sio.event
    async def disconnect():
        logging.debug("Socket.IO disconnected")
        logging.info("Socket.IO disconnected.")

    async def handle_join_response(response_dict):
        nonlocal server_info
        logging.debug(f"Received join_call response")
        logging.info(f"Received response for 'join_call' (two-way audio).")
        logging.info(f"Full response: {response_dict}")
        try:
            if "result" in response_dict:
                server_info = response_dict["result"]
                # Step 1: Setup video-only connection first (like video_watch_only)
                await setup_video_only_phase(server_info)

                # CRITICAL FIX: Connect send transport BEFORE pickup to match mobile app sequence
                # Mobile app connects sendTransport before pickup, then pickup creates producer on connected transport
                logging.info("Phase 2a: Setting up send transport for audio transmission...")
                await setup_send_transport_for_transmission()  # Create peer connection and audio track

                logging.info("Phase 2b: Connecting send transport BEFORE pickup (matching mobile app sequence)...")
                await connect_send_transport_before_pickup()  # Connect transport first

                logging.info("Phase 2c: Sending pickup event to create producer on connected transport...")
                await send_pickup_event()  # Pickup creates producer on already-connected transport
                # Note: Audio transport setup is now done in pickup response handler
            else:
                error_info = response_dict.get("error", {})
                error_code = error_info.get("code", "")
                
                # Handle room not found error
                if error_code == "exception_call_join_failure" and "do not exist" in error_info.get("message", ""):
                    logging.error("Room not found. Check that you're using the correct SERVER_URL for your region.")
                    logging.info("Try a different call divert server URL or wait for a new doorbell press.")
                else:
                    logging.error(f"Server returned error response: {response_dict}")
                
                call_failed_event.set()
        except Exception as e:
            logging.error(f"Error parsing join response: {e}", exc_info=True)
            call_failed_event.set()

    async def setup_video_only_phase(server_info):
        """Phase 1: Setup video-only connection exactly like working video_watch_only"""
        try:
            logging.info("Phase 1: Setting up video-only connection (like video_watch_only)...")
            # Apply optimized ICE configuration for faster connection establishment
            optimized_config = create_optimized_rtc_configuration(server_info["iceServers"])
            pc.configuration = optimized_config
            logging.info(f"Applied optimized ICE configuration to video PC with {len(optimized_config.iceServers)} ICE servers")
            
            # Use the exact same video-only setup as the working webrtc_client.py
            video_transport_info = server_info["recvTransportVideo"]
            video_producer_id = server_info["producerIdVideo"]
            
            # 1. transport_consume for VIDEO only
            video_consume_future = asyncio.Future()
            transport_consume_data = {
                "transportId": video_transport_info["id"], 
                "producerId": video_producer_id, 
                "rtpCapabilities": RTP_CAPABILITIES
            }
            logging.debug(f"Sending transport_consume for video")
            
            def video_consume_callback(data):
                logging.debug(f"Received video transport_consume response")
                video_consume_future.set_result(data)
            
            await sio.emit("transport_consume", transport_consume_data, callback=video_consume_callback)
            video_consumer_params = await video_consume_future
            
            # 2. Build video-only SDP exactly like working client
            ice, dtls = video_transport_info["iceParameters"], video_transport_info["dtlsParameters"]
            vid_rtp = video_consumer_params["result"]["rtpParameters"]
            video_payload_type = vid_rtp['codecs'][0]['payloadType']
            
            sdp_str = (f"v=0\r\no=- 5133548076695286524 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=msid-semantic: WMS\r\n"
                       f"a=ice-ufrag:{ice['usernameFragment']}\r\na=ice-pwd:{ice['password']}\r\n" +
                       "".join([f"a=fingerprint:{fp['algorithm']} {fp['value'].upper()}\r\n" for fp in dtls['fingerprints']]) +
                       f"a=setup:actpass\r\nm=video 9 UDP/TLS/RTP/SAVPF {video_payload_type}\r\nc=IN IP4 0.0.0.0\r\na=rtcp-mux\r\n" +
                       "".join([f"a=candidate:{c['foundation']} 1 {c['protocol']} {c['priority']} {c['ip']} {c['port']} typ {c['type']}\r\n" for c in video_transport_info.get("iceCandidates", [])]) +
                       f"a=end-of-candidates\r\na=mid:video\r\na=sendrecv\r\n" +
                       "".join([f"a=extmap:{ext['id']} {ext['uri']}\r\n" for ext in vid_rtp["headerExtensions"]]) +
                       f"a=rtpmap:{video_payload_type} H264/90000\r\n"
                       f"a=fmtp:{video_payload_type} level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=42e01f\r\n"
                       f"a=ssrc:{vid_rtp['encodings'][0]['ssrc']} cname:{vid_rtp['rtcp']['cname']}\r\n")
            
            # 3. WebRTC negotiation for video only
            from aiortc import RTCSessionDescription
            remote_description = RTCSessionDescription(sdp=sdp_str, type="offer")
            await pc.setRemoteDescription(remote_description)
            answer = await pc.createAnswer()
            await pc.setLocalDescription(answer)
            
            # 4. Connect video receive transport
            dtls_transport = pc.getTransceivers()[0].receiver.transport
            local_dtls_params = {"role": "client", "fingerprints": [{"algorithm": fp.algorithm, "value": fp.value} for fp in dtls_transport.getLocalParameters().fingerprints]}
            transport_connect_data = {"transportId": video_transport_info["id"], "dtlsParameters": local_dtls_params}
            logging.debug(f"Connecting video transport")
            await sio.emit("transport_connect", transport_connect_data)
            
            logging.info("Phase 1 complete: Video receive transport connected")
            
            # Check Socket.IO connection health after Phase 1
            if not sio.connected:
                logging.error("Socket.IO connection lost during Phase 1 setup")
                call_failed_event.set()
                return
            else:
                logging.info("✅ Socket.IO connection still active after Phase 1")
            
        except Exception as e:
            logging.error(f"Error during video-only setup: {e}", exc_info=True)
            call_failed_event.set()

    async def setup_send_transport_for_transmission():
        """Create audio track for transmission (simplified approach)"""
        try:
            logging.info("Creating audio track for transmission...")
            
            # Apply optimized ICE configuration for faster connection establishment
            optimized_config = create_optimized_rtc_configuration(server_info["iceServers"])
            send_pc.configuration = optimized_config
            logging.info(f"Applied optimized ICE configuration to send PC with {len(optimized_config.iceServers)} ICE servers")

            # CRITICAL FIX: Generate pickup SSRC/CNAME BEFORE any SDP operations
            # This ensures SDP, transport_connect, and pickup all use the same values
            nonlocal pickup_ssrc, pickup_cname
            pickup_ssrc = generate_ssrc()
            pickup_cname = generate_random_string(16)
            
            logging.info(f"📊 Pre-generated pickup parameters (BEFORE SDP): SSRC={pickup_ssrc}, CNAME={pickup_cname}")

            # 🎯 STAGE 0: Early CNAME override right after generation
            await early_cname_override()
            verify_cname_override("(after pickup generation)")

            # Import audio track creation
            from audio_tracks import create_audio_track

            # 🎵 AUDIO SOURCE SELECTION: Use configured audio source
            nonlocal send_audio_track
            logging.info(f"🎵 AUDIO SOURCE - Using configured source: {config.AUDIO_SOURCE}")
            
            if config.AUDIO_SOURCE == "onvif":
                # Use real-time ONVIF audio from the Go RTSP proxy
                send_audio_track = create_audio_track(track_type="onvif")
                logging.info("🎤 ONVIF Audio Track created - will receive real-time audio from ONVIF clients")
            elif config.AUDIO_SOURCE == "file":
                # Use file-based audio (existing behavior)
                send_audio_track = create_audio_track(file_path="test_data/espetaculo_1min.wav", track_type="file")
                logging.info("📁 File Audio Track created - using WAV file")
            else:
                # Use other track types (tone, speech_like, etc.)
                send_audio_track = create_audio_track(track_type=config.AUDIO_SOURCE)
                logging.info(f"🎵 {config.AUDIO_SOURCE.title()} Audio Track created")

            # 🔍 WEBRTC DEBUG: Log track details before adding
            logging.info("🔍 WEBRTC DEBUG - FileAudioTrack Details:")
            logging.info(f"🔍 Track kind: {send_audio_track.kind}")
            logging.info(f"🔍 Track id: {send_audio_track.id}")
            logging.info(f"🔍 Track readyState: {send_audio_track.readyState}")
            if hasattr(send_audio_track, '_frame_count'):
                logging.info(f"🔍 Track frame count: {send_audio_track._frame_count}")
            if hasattr(send_audio_track, '_file_path'):
                logging.info(f"🔍 Track file path: {send_audio_track._file_path}")

            # Add audio track to send peer connection (will be configured later)
            sender = send_pc.addTrack(send_audio_track)
            
            # 🎯 VERIFICATION: Check CNAME after track addition
            verify_cname_override("(after addTrack)")

            # 🔍 WEBRTC DEBUG: Log sender details after adding track
            logging.info("🔍 WEBRTC DEBUG - Sender Details After addTrack:")
            logging.info(f"🔍 Sender track: {sender.track}")
            logging.info(f"🔍 Sender transport: {sender.transport}")
            
            # 📦 COMPREHENSIVE PEER CONNECTION RTCP/CNAME INSPECTION
            async def inspect_peer_connection_cname():
                """Deep inspection of peer connection for CNAME configuration"""
                try:
                    logging.info("📦 PC CNAME INSPECTION - Analyzing peer connection internals...")
                    
                    # Check peer connection attributes
                    pc_attrs = [attr for attr in dir(send_pc) if 'rtcp' in attr.lower() or 'cname' in attr.lower() or 'ssrc' in attr.lower()]
                    if pc_attrs:
                        logging.info(f"📦 PC CNAME INSPECTION - PC RTCP attributes: {pc_attrs}")
                        for attr in pc_attrs:
                            try:
                                value = getattr(send_pc, attr)
                                logging.info(f"📦 PC CNAME INSPECTION - send_pc.{attr} = {value}")
                            except Exception as e:
                                logging.debug(f"📦 PC CNAME INSPECTION - Cannot access send_pc.{attr}: {e}")
                    
                    # Check internal peer connection objects
                    internal_attrs = ['_RTCPeerConnection__dtlsTransports', '_RTCPeerConnection__iceTransports', '_RTCPeerConnection__transceivers']
                    for attr in internal_attrs:
                        if hasattr(send_pc, attr):
                            try:
                                value = getattr(send_pc, attr)
                                logging.info(f"📦 PC CNAME INSPECTION - Found internal attr {attr}: {value}")
                            except Exception as e:
                                logging.debug(f"📦 PC CNAME INSPECTION - Cannot access {attr}: {e}")
                    
                    # Check configuration object
                    if hasattr(send_pc, 'configuration'):
                        config = send_pc.configuration
                        logging.info(f"📦 PC CNAME INSPECTION - PC configuration: {config}")
                        
                        # Check if configuration has RTCP-related properties
                        config_attrs = [attr for attr in dir(config) if 'rtcp' in attr.lower() or 'cname' in attr.lower()]
                        if config_attrs:
                            for attr in config_attrs:
                                try:
                                    value = getattr(config, attr)
                                    logging.info(f"📦 PC CNAME INSPECTION - config.{attr} = {value}")
                                except Exception as e:
                                    logging.debug(f"📦 PC CNAME INSPECTION - Cannot access config.{attr}: {e}")
                    
                    # Check getters and stats at PC level
                    try:
                        pc_stats = await send_pc.getStats()
                        for s in pc_stats:
                            stat = pc_stats[s]
                            # Look for any CNAME-like attributes in PC stats
                            cname_candidates = []
                            for attr in dir(stat):
                                if 'cname' in attr.lower() or 'canonical' in attr.lower():
                                    try:
                                        value = getattr(stat, attr)
                                        cname_candidates.append((attr, value))
                                    except:
                                        pass
                            
                            if cname_candidates:
                                logging.info(f"📦 PC CNAME INSPECTION - PC stat {s} CNAME candidates: {cname_candidates}")
                    except Exception as e:
                        logging.debug(f"📦 PC CNAME INSPECTION - Cannot get PC stats: {e}")
                    
                    logging.info("📦 PC CNAME INSPECTION - Complete")
                        
                except Exception as e:
                    logging.error(f"📦 PC CNAME INSPECTION - Error: {e}")
            
            # Run peer connection CNAME inspection
            await inspect_peer_connection_cname()
            
            # CRITICAL FIX: Force sender to use our pickup SSRC instead of auto-generated
            try:
                # Check if sender has SSRC property that can be set
                if hasattr(sender, '_ssrc'):
                    original_ssrc = sender._ssrc
                    sender._ssrc = pickup_ssrc
                    logging.info(f"📊 CRITICAL FIX: Changed sender SSRC from {original_ssrc} to {pickup_ssrc}")
                elif hasattr(sender, 'ssrc'):
                    original_ssrc = sender.ssrc
                    sender.ssrc = pickup_ssrc
                    logging.info(f"📊 CRITICAL FIX: Changed sender SSRC from {original_ssrc} to {pickup_ssrc}")
                else:
                    logging.warning("📊 WARNING: Cannot find sender SSRC property to modify")
                    
                # CRITICAL FIX: Comprehensive CNAME override attempts
                cname_success = False
                cname_attempts = [
                    # Try sender object properties
                    ('sender._cname', lambda: setattr(sender, '_cname', pickup_cname)),
                    ('sender.cname', lambda: setattr(sender, 'cname', pickup_cname)),
                    # Try internal transport/stream properties
                    ('sender._transport.cname', lambda: setattr(sender._transport, 'cname', pickup_cname) if hasattr(sender, '_transport') and sender._transport else None),
                    ('sender._stream.cname', lambda: setattr(sender._stream, 'cname', pickup_cname) if hasattr(sender, '_stream') and sender._stream else None),
                    # Try internal RTP properties
                    ('sender._rtp.cname', lambda: setattr(sender._rtp, 'cname', pickup_cname) if hasattr(sender, '_rtp') and sender._rtp else None),
                    ('sender._rtp._cname', lambda: setattr(sender._rtp, '_cname', pickup_cname) if hasattr(sender, '_rtp') and sender._rtp else None),
                    # Try track properties
                    ('sender.track.cname', lambda: setattr(sender.track, 'cname', pickup_cname) if hasattr(sender.track, 'cname') else None),
                    ('sender.track._cname', lambda: setattr(sender.track, '_cname', pickup_cname) if hasattr(sender.track, '_cname') else None),
                ]
                
                for prop_name, setter_func in cname_attempts:
                    try:
                        # Get original value first
                        prop_path = prop_name.split('.')
                        obj = sender
                        for attr in prop_path[1:-1]:  # Navigate to parent object
                            if hasattr(obj, attr):
                                obj = getattr(obj, attr)
                            else:
                                obj = None
                                break
                        
                        if obj and hasattr(obj, prop_path[-1]):
                            original_cname = getattr(obj, prop_path[-1])
                            setter_func()
                            logging.info(f"📊 CRITICAL FIX: Changed {prop_name} from {original_cname} to {pickup_cname}")
                            cname_success = True
                            break
                    except Exception as prop_e:
                        logging.debug(f"📊 DEBUG: {prop_name} not accessible: {prop_e}")
                        continue
                
                if not cname_success:
                    logging.warning(f"📊 WARNING: Could not find any CNAME property to override on sender")
                    
                    # 🎯 DIRECT NAME-MANGLED ATTRIBUTE OVERRIDE (TARGETED FIX)
                    logging.info("🎯 DIRECT OVERRIDE - Modifying actual name-mangled CNAME attributes...")
                    
                    # Direct modification of the actual private mangled attributes found by RTCP inspection
                    cname_override_success = False
                    
                    # Stage 1: Override peer connection level CNAME (before any operations)
                    try:
                        if hasattr(send_pc, '_RTCPeerConnection__cname'):
                            original_pc_cname = send_pc._RTCPeerConnection__cname
                            send_pc._RTCPeerConnection__cname = pickup_cname
                            logging.info(f"🎯 DIRECT OVERRIDE SUCCESS - PC CNAME: {original_pc_cname} → {pickup_cname}")
                            cname_override_success = True
                        else:
                            logging.warning("🎯 DIRECT OVERRIDE - _RTCPeerConnection__cname not found")
                    except Exception as e:
                        logging.error(f"🎯 DIRECT OVERRIDE - PC CNAME failed: {e}")
                    
                    # Stage 2: Override sender level CNAME (direct access to private attribute)
                    try:
                        if hasattr(sender, '_RTCRtpSender__cname'):
                            original_sender_cname = sender._RTCRtpSender__cname
                            sender._RTCRtpSender__cname = pickup_cname
                            logging.info(f"🎯 DIRECT OVERRIDE SUCCESS - Sender CNAME: {original_sender_cname} → {pickup_cname}")
                            cname_override_success = True
                        else:
                            logging.warning("🎯 DIRECT OVERRIDE - _RTCRtpSender__cname not found")
                    except Exception as e:
                        logging.error(f"🎯 DIRECT OVERRIDE - Sender CNAME failed: {e}")
                    
                    # Report override results
                    if cname_override_success:
                        logging.info("🎯 DIRECT OVERRIDE - CNAME override completed successfully!")
                    else:
                        logging.error("🎯 DIRECT OVERRIDE - All CNAME override attempts failed!")
                        
                        # 🐒 SCOPE-FIXED MONKEY PATCHING (fallback approach)
                        logging.info("🐒 MONKEY PATCH - Attempting aiortc module patching as fallback...")
                        try:
                            import aiortc.rtcrtpsender
                            import aiortc.rtcpeerconnection
                            
                            # Try to find and override CNAME generation functions
                            modules_to_patch = [aiortc.rtcrtpsender, aiortc.rtcpeerconnection]
                            
                            for module in modules_to_patch:
                                # Look for functions that might generate CNAMEs
                                potential_cname_funcs = [attr for attr in dir(module) if 'cname' in attr.lower() or 'random' in attr.lower()]
                                
                                for func_name in potential_cname_funcs:
                                    try:
                                        original_func = getattr(module, func_name)
                                        if callable(original_func):
                                            # Override to always return our pickup CNAME
                                            setattr(module, func_name, lambda *args, **kwargs: pickup_cname)
                                            logging.info(f"🐒 MONKEY PATCH SUCCESS - Overrode {module.__name__}.{func_name}")
                                    except Exception as e:
                                        logging.debug(f"🐒 MONKEY PATCH - Could not patch {module.__name__}.{func_name}: {e}")
                                        
                        except Exception as e:
                            logging.debug(f"🐒 MONKEY PATCH - Module patching failed: {e}")
                        
                    # Legacy fallback attempts (for completeness)
                    legacy_override_attempts = [
                        ('send_audio_track._cname', lambda: setattr(send_audio_track, '_cname', pickup_cname)),
                        ('send_audio_track.cname', lambda: setattr(send_audio_track, 'cname', pickup_cname)),
                    ]
                    
                    for attempt_name, attempt_func in legacy_override_attempts:
                        try:
                            attempt_func()
                            logging.info(f"🎯 LEGACY OVERRIDE - Successfully executed {attempt_name}")
                        except Exception as e:
                            logging.debug(f"🎯 LEGACY OVERRIDE - {attempt_name} failed: {e}")
                    
                    # 🎯 VERIFICATION: Check CNAME after all override attempts
                    verify_cname_override("(after all overrides)")
                    
            except Exception as e:
                logging.error(f"📊 ERROR: Failed to set sender SSRC/CNAME: {e}")

            # Define monkey patching function for CNAME generation
            def monkey_patch_aiortc_cname_generation(target_cname):
                """Attempt to monkey patch aiortc's CNAME generation functions"""
                try:
                    import aiortc.rtcrtpsender
                    import aiortc.rtcpeerconnection
                    
                    # Try to find and override CNAME generation functions
                    modules_to_patch = [aiortc.rtcrtpsender, aiortc.rtcpeerconnection]
                    
                    for module in modules_to_patch:
                        # Look for functions that might generate CNAMEs
                        potential_cname_funcs = [attr for attr in dir(module) if 'cname' in attr.lower() or 'random' in attr.lower()]
                        
                        for func_name in potential_cname_funcs:
                            try:
                                original_func = getattr(module, func_name)
                                if callable(original_func):
                                    # Override to always return our pickup CNAME
                                    setattr(module, func_name, lambda *args, **kwargs: target_cname)
                                    logging.info(f"📦 MONKEY PATCH - Overrode {module.__name__}.{func_name}")
                            except Exception as e:
                                logging.debug(f"📦 MONKEY PATCH - Could not patch {module.__name__}.{func_name}: {e}")
                                
                except Exception as e:
                    logging.debug(f"📦 MONKEY PATCH - Monkey patching failed: {e}")

            # 🔍 WEBRTC DEBUG: Get the actual transceiver from the peer connection
            transceivers = send_pc.getTransceivers()
            logging.info(f"🔍 WEBRTC DEBUG - Send PC has {len(transceivers)} transceivers after addTrack:")
            for i, transceiver in enumerate(transceivers):
                logging.info(f"🔍 Transceiver {i}: kind={transceiver.kind}, direction={transceiver.direction}")
                logging.info(f"🔍   mid={transceiver.mid}, sender={transceiver.sender}")

                # 🔍 WEBRTC DEBUG: Check if we need to set transceiver direction explicitly
                if transceiver.kind == 'audio':
                    if transceiver.direction != 'sendonly' and transceiver.direction != 'sendrecv':
                        logging.warning(f"🔍 Audio transceiver direction is '{transceiver.direction}' - should be 'sendonly' or 'sendrecv' for transmission")
                        logging.info("🔍 Attempting to set audio transceiver direction to 'sendonly'")
                        try:
                            transceiver.direction = 'sendonly'
                            logging.info(f"🔍 Audio transceiver direction set to: {transceiver.direction}")
                        except Exception as e:
                            logging.warning(f"🔍 Could not set audio transceiver direction: {e}")
                    
                    # CRITICAL FIX: Try to set SSRC on transceiver as well
                    try:
                        if hasattr(transceiver, '_ssrc'):
                            original_ssrc = transceiver._ssrc
                            transceiver._ssrc = pickup_ssrc
                            logging.info(f"📊 CRITICAL FIX: Changed transceiver SSRC from {original_ssrc} to {pickup_ssrc}")
                        elif hasattr(transceiver, 'ssrc'):
                            original_ssrc = transceiver.ssrc
                            transceiver.ssrc = pickup_ssrc
                            logging.info(f"📊 CRITICAL FIX: Changed transceiver SSRC from {original_ssrc} to {pickup_ssrc}")
                        
                        # Also try the sender within the transceiver
                        if transceiver.sender and hasattr(transceiver.sender, '_ssrc'):
                            original_ssrc = transceiver.sender._ssrc
                            transceiver.sender._ssrc = pickup_ssrc
                            logging.info(f"📊 CRITICAL FIX: Changed transceiver.sender SSRC from {original_ssrc} to {pickup_ssrc}")
                        elif transceiver.sender and hasattr(transceiver.sender, 'ssrc'):
                            original_ssrc = transceiver.sender.ssrc
                            transceiver.sender.ssrc = pickup_ssrc
                            logging.info(f"📊 CRITICAL FIX: Changed transceiver.sender SSRC from {original_ssrc} to {pickup_ssrc}")
                        
                        # CRITICAL FIX: Try CNAME override on transceiver and its sender
                        transceiver_cname_success = False
                        transceiver_cname_attempts = [
                            # Try transceiver properties
                            ('transceiver._cname', lambda: setattr(transceiver, '_cname', pickup_cname)),
                            ('transceiver.cname', lambda: setattr(transceiver, 'cname', pickup_cname)),
                            # Try transceiver.sender properties (comprehensive)
                            ('transceiver.sender._cname', lambda: setattr(transceiver.sender, '_cname', pickup_cname) if transceiver.sender else None),
                            ('transceiver.sender.cname', lambda: setattr(transceiver.sender, 'cname', pickup_cname) if transceiver.sender else None),
                            ('transceiver.sender._transport.cname', lambda: setattr(transceiver.sender._transport, 'cname', pickup_cname) if transceiver.sender and hasattr(transceiver.sender, '_transport') and transceiver.sender._transport else None),
                            ('transceiver.sender._rtp.cname', lambda: setattr(transceiver.sender._rtp, 'cname', pickup_cname) if transceiver.sender and hasattr(transceiver.sender, '_rtp') and transceiver.sender._rtp else None),
                            ('transceiver.sender._rtp._cname', lambda: setattr(transceiver.sender._rtp, '_cname', pickup_cname) if transceiver.sender and hasattr(transceiver.sender, '_rtp') and transceiver.sender._rtp else None),
                        ]
                        
                        for prop_name, setter_func in transceiver_cname_attempts:
                            try:
                                # Get original value first
                                prop_path = prop_name.split('.')
                                obj = transceiver
                                for attr in prop_path[1:-1]:  # Navigate to parent object
                                    if hasattr(obj, attr):
                                        obj = getattr(obj, attr)
                                    else:
                                        obj = None
                                        break
                                
                                if obj and hasattr(obj, prop_path[-1]):
                                    original_cname = getattr(obj, prop_path[-1])
                                    setter_func()
                                    logging.info(f"📊 CRITICAL FIX: Changed {prop_name} from {original_cname} to {pickup_cname}")
                                    transceiver_cname_success = True
                                    break
                            except Exception as prop_e:
                                logging.debug(f"📊 DEBUG: {prop_name} not accessible: {prop_e}")
                                continue
                        
                        if not transceiver_cname_success:
                            logging.warning(f"📊 WARNING: Could not find any CNAME property to override on transceiver")
                            
                    except Exception as e:
                        logging.warning(f"📊 WARNING: Could not set transceiver SSRC/CNAME: {e}")

            logging.info("Audio track created and added to send peer connection")

            # DIAGNOSTIC: Verify track was added correctly
            transceivers = send_pc.getTransceivers()
            logging.info(f"🔍 DIAGNOSTIC - Send PC now has {len(transceivers)} transceivers")
            for i, transceiver in enumerate(transceivers):
                if transceiver.sender and transceiver.sender.track:
                    track = transceiver.sender.track
                    try:
                        track_id = getattr(track, 'id', 'unknown')
                        track_kind = getattr(track, 'kind', 'unknown')
                        track_state = getattr(track, 'readyState', 'unknown')
                        logging.info(f"🔍 DIAGNOSTIC - Transceiver {i}: track_id={track_id}, kind={track_kind}, readyState={track_state}")

                        # Check track class
                        track_class = track.__class__.__name__ if hasattr(track, '__class__') else 'unknown'
                        logging.info(f"🔍 DIAGNOSTIC - Track class: {track_class}")

                        # Start monitoring this specific track
                        if hasattr(track, '_frame_count'):
                            logging.info(f"🔍 DIAGNOSTIC - Track is FileAudioTrack with {track._frame_count} frames generated so far")
                    except Exception as e:
                        logging.error(f"🔍 DIAGNOSTIC - Error checking transceiver {i}: {e}")

            logging.info("Send transport setup complete")

        except Exception as e:
            logging.error(f"Error setting up send transport: {e}", exc_info=True)
            raise

    async def connect_send_transport_before_pickup():
        """Connect send transport BEFORE pickup event to match mobile app sequence"""
        try:
            logging.info("Connecting send transport BEFORE pickup (matching mobile app sequence - transport must be connected before producer creation)...")

            send_transport_info = server_info["sendTransport"]

            # Create a temporary offer just to generate DTLS certificate for transport_connect
            # We'll do the real negotiation later in setup_audio_transmission_peer_connection()
            logging.info("Creating temporary offer to generate DTLS fingerprint for transport_connect...")

            # Create simple offer to generate DTLS certificate
            offer = await send_pc.createOffer()
            
            # CRITICAL FIX: Replace auto-generated SSRC with pickup SSRC for consistency
            if pickup_ssrc and pickup_cname:
                logging.info(f"📊 Replacing auto-generated SSRC with pickup SSRC: {pickup_ssrc}")
                original_sdp = offer.sdp
                
                # Replace SSRC and CNAME in SDP
                import re
                # Find and replace SSRC line
                ssrc_pattern = r'a=ssrc:(\d+) cname:([^\r\n]+)'
                match = re.search(ssrc_pattern, original_sdp)
                
                if match:
                    old_ssrc = match.group(1)
                    old_cname = match.group(2)
                    logging.info(f"📊 Original SDP: SSRC={old_ssrc}, CNAME={old_cname}")
                    
                    # Replace with pickup values
                    new_sdp = re.sub(ssrc_pattern, f'a=ssrc:{pickup_ssrc} cname:{pickup_cname}', original_sdp)
                    
                    # Create new offer with aligned SSRC/CNAME
                    from aiortc import RTCSessionDescription
                    offer = RTCSessionDescription(sdp=new_sdp, type=offer.type)
                    
                    logging.info(f"📊 Modified SDP: SSRC={pickup_ssrc}, CNAME={pickup_cname}")
                else:
                    logging.warning("📊 Could not find SSRC in SDP to replace")
            else:
                logging.warning("📊 No pickup SSRC/CNAME available for SDP modification")
            
            await send_pc.setLocalDescription(offer)

            # Extract real DTLS fingerprint from the send peer connection
            real_fingerprint = None
            if len(send_pc.getTransceivers()) > 0:
                dtls_transport = send_pc.getTransceivers()[0].sender.transport
                if dtls_transport:
                    local_dtls_params = dtls_transport.getLocalParameters()

                    # 🔍 WEBRTC DEBUG: Log DTLS parameters
                    logging.info("🔍 WEBRTC DEBUG - Local DTLS Parameters:")
                    if local_dtls_params:
                        logging.info(f"🔍 DTLS Role: {local_dtls_params.role if hasattr(local_dtls_params, 'role') else 'Unknown'}")
                        if local_dtls_params.fingerprints:
                            logging.info(f"🔍 DTLS Fingerprints ({len(local_dtls_params.fingerprints)} total):")
                            for i, fp in enumerate(local_dtls_params.fingerprints):
                                logging.info(f"🔍   Fingerprint {i}: {fp.algorithm} = {fp.value}")
                        else:
                            logging.warning("🔍 No DTLS fingerprints found!")
                    else:
                        logging.warning("🔍 No local DTLS parameters found!")

                    if local_dtls_params and local_dtls_params.fingerprints:
                        # Find SHA-256 fingerprint
                        for fp in local_dtls_params.fingerprints:
                            if fp.algorithm.lower() == "sha-256":
                                real_fingerprint = fp.value
                                break

            if not real_fingerprint:
                raise Exception("Could not extract real DTLS fingerprint from send peer connection")

            # Connect send transport with real DTLS parameters
            send_transport_connect_data = {
                "transportId": send_transport_info["id"],
                "dtlsParameters": {
                    "role": "server",
                    "fingerprints": [{"algorithm": "sha-256", "value": real_fingerprint}]
                }
            }

            logging.info(f"Connecting send transport {send_transport_info['id']} with REAL DTLS fingerprint: {real_fingerprint}")
            logging.info("✅ Using SAME peer connection for fingerprint extraction and DTLS handshake (no certificate mismatch)")

            # Create future to wait for transport_connect response
            transport_connect_future = asyncio.Future()

            def transport_connect_callback(*args):
                logging.info(f"Received transport_connect response: {args}")
                transport_connect_future.set_result(args)

            await sio.emit("transport_connect", send_transport_connect_data, callback=transport_connect_callback)

            # Wait for transport_connect response
            try:
                transport_response = await asyncio.wait_for(transport_connect_future, timeout=10.0)
                logging.info(f"✅ Send transport connection acknowledged by server: {transport_response}")

                # Now setup the actual audio transmission using aiortc
                await setup_audio_transmission_peer_connection()

                logging.info("✅ Send transport connected BEFORE pickup - ready for producer creation on next pickup event!")
                
                # 🎯 VERIFICATION: Check CNAME after transport connection
                verify_cname_override("(after transport connection)")

            except asyncio.TimeoutError:
                logging.error("❌ Timeout waiting for transport_connect response from server")
                raise Exception("MediaSoup server did not respond to transport_connect")
            except Exception as e:
                logging.error(f"❌ Error in transport_connect response: {e}")
                raise

        except Exception as e:
            logging.error(f"Error connecting send transport before pickup: {e}", exc_info=True)
            raise



    async def setup_audio_transmission_peer_connection():
        """Setup WebRTC peer connection for audio transmission - complete negotiation with server's answer"""
        try:
            logging.info("Setting up audio transmission peer connection - completing offer/answer negotiation...")

            send_transport_info = server_info["sendTransport"]

            # Since send_pc already has a local OFFER from transport_connect phase,
            # we need to set the server's response as a remote ANSWER (not offer!)
            send_ice = send_transport_info["iceParameters"]
            send_dtls = send_transport_info["dtlsParameters"]

            # Build remote SDP as ANSWER using send transport parameters
            remote_sdp = (
                f"v=0\r\n"
                f"o=- 1234567890 1 IN IP4 127.0.0.1\r\n"
                f"s=-\r\n"
                f"t=0 0\r\n"
                f"a=ice-ufrag:{send_ice['usernameFragment']}\r\n"
                f"a=ice-pwd:{send_ice['password']}\r\n"
            )

            # Add DTLS fingerprints
            for fp in send_dtls['fingerprints']:
                remote_sdp += f"a=fingerprint:{fp['algorithm']} {fp['value'].upper()}\r\n"

            remote_sdp += (
                f"a=setup:active\r\n"  # Server is active (opposite of client's passive)
                f"m=audio 9 UDP/TLS/RTP/SAVPF 8\r\n"
                f"c=IN IP4 0.0.0.0\r\n"
                f"a=rtcp-mux\r\n"
                f"a=mid:0\r\n"
                f"a=recvonly\r\n"  # Server receives audio from us (keep this correct!)
                f"a=rtpmap:8 PCMA/8000\r\n"
            )

            # Add ICE candidates
            for candidate in send_transport_info.get("iceCandidates", []):
                remote_sdp += f"a=candidate:{candidate['foundation']} 1 {candidate['protocol']} {candidate['priority']} {candidate['ip']} {candidate['port']} typ {candidate['type']}\r\n"

            remote_sdp += "a=end-of-candidates\r\n"

            # 🔍 WEBRTC DEBUG: Log server's SDP answer before setting
            logging.info("🔍 WEBRTC DEBUG - Server's SDP Answer Analysis:")
            for i, line in enumerate(remote_sdp.split('\r\n')):
                if line.strip():
                    logging.info(f"🔍 Server SDP {i:2d}: {line}")

            # Set remote description as ANSWER (since we already have local offer)
            from aiortc import RTCSessionDescription
            remote_description = RTCSessionDescription(sdp=remote_sdp, type="answer")
            await send_pc.setRemoteDescription(remote_description)

            # No need to create/set local description - we already have the offer from transport_connect phase
            logging.info("✅ Completed offer/answer negotiation - send_pc ready for audio transmission")

            # 🔍 WEBRTC DEBUG: Check for critical SDP attributes
            if 'a=sendonly' in remote_sdp:
                logging.error("🚨 CRITICAL: Server SDP contains 'a=sendonly' - server is rejecting our audio transmission!")
            if 'a=recvonly' in remote_sdp:
                logging.info("✅ Server SDP contains 'a=recvonly' - server will receive our audio")
            if 'a=sendrecv' in remote_sdp:
                logging.info("✅ Server SDP contains 'a=sendrecv' - bidirectional audio")

            # 🔍 WEBRTC DEBUG: Analyze audio codec in server response
            audio_lines = [line for line in remote_sdp.split('\r\n') if 'audio' in line.lower() or line.startswith('a=rtpmap:8') or line.startswith('m=audio')]
            logging.info("🔍 WEBRTC DEBUG - Server's audio-specific SDP lines:")
            for line in audio_lines:
                if line.strip():
                    logging.info(f"🔍 Server Audio: {line}")

            logging.info("✅ Audio transmission peer connection established using aiortc negotiation")

        except Exception as e:
            logging.error(f"Error setting up audio transmission peer connection: {e}", exc_info=True)

    async def connect_audio_track_to_producer(producer_id):
        """Connect our FileAudioTrack to the existing MediaSoup producer created by the server"""
        try:
            logging.info(f"🎵 Connecting FileAudioTrack to existing producer: {producer_id}")

            # The key insight: The server already created a producer during pickup
            # We need to make our RTCRtpSender start transmitting to that producer

            # Get the send peer connection and its transceivers
            transceivers = send_pc.getTransceivers()
            logging.info(f"🎵 Send PC has {len(transceivers)} transceivers")

            for i, transceiver in enumerate(transceivers):
                if transceiver.sender and transceiver.sender.track:
                    track = transceiver.sender.track
                    logging.info(f"🎵 Transceiver {i}: track={track.__class__.__name__}, kind={getattr(track, 'kind', 'unknown')}")

                    # Check if this is our FileAudioTrack
                    if hasattr(track, '_frame_count'):  # FileAudioTrack has this attribute
                        logging.info(f"🎵 Found our FileAudioTrack in transceiver {i}")

                        # The magic: Force the sender to start transmitting
                        # This should trigger recv() calls on our FileAudioTrack
                        sender = transceiver.sender

                        # Check sender state
                        logging.info(f"🎵 Sender transport: {sender.transport}")
                        logging.info(f"🎵 Sender transport state: {sender.transport.state if sender.transport else 'None'}")

                        # The sender should already be connected via our send transport
                        # The missing piece might be that we need to explicitly start transmission

                        # Try to get sender stats to see if it's active
                        try:
                            stats = await sender.getStats()
                            logging.info(f"🎵 Sender stats available: {len(stats) if stats else 0} entries")
                            for stat in stats:
                                if hasattr(stat, 'type') and 'outbound' in str(stat.type):
                                    logging.info(f"🎵 Outbound stat: {stat.type}")
                        except Exception as e:
                            logging.info(f"🎵 Could not get sender stats: {e}")

                        # The connection should already be established
                        # If recv() is still not called, there might be another issue
                        logging.info(f"🎵 FileAudioTrack should now be connected to producer {producer_id}")

                        # Start monitoring for recv() calls
                        asyncio.create_task(monitor_track_activity(track))

                        break
            else:
                logging.warning("🎵 Could not find FileAudioTrack in send peer connection transceivers")

        except Exception as e:
            logging.error(f"❌ Error connecting audio track to producer: {e}", exc_info=True)

    async def monitor_track_activity(track):
        """Monitor if the track starts generating frames"""
        try:
            logging.info("🎵 Starting track activity monitoring...")
            initial_frame_count = getattr(track, '_frame_count', 0)

            for i in range(30):  # Monitor for 30 seconds
                await asyncio.sleep(1)
                current_frame_count = getattr(track, '_frame_count', 0)

                if current_frame_count > initial_frame_count:
                    logging.info(f"🎵 ✅ SUCCESS! Track is generating frames: {current_frame_count}")
                    return
                elif i % 5 == 0:  # Log every 5 seconds
                    logging.info(f"🎵 Track monitoring ({i}s): still no frames generated")

            logging.warning("🎵 ⚠️ Track monitoring complete: No frames generated in 30 seconds")

        except Exception as e:
            logging.error(f"Error in track activity monitoring: {e}")

    async def send_pickup_event():
        """Phase 2c: Send pickup event to create producer on already-connected send transport"""
        try:
            logging.info("Phase 2c: Sending pickup event to create producer (send transport already connected)...")
            
            # 🎯 VERIFICATION: Final CNAME check before pickup event (critical moment)
            verify_cname_override("(FINAL CHECK before pickup event)")

            # Check if Socket.IO connection is still active
            if not sio.connected:
                logging.error("Socket.IO connection lost before pickup event")
                call_failed_event.set()
                return

            # Note: Send transport already connected in Phase 2b - pickup creates producer on connected transport

            # Send pickup event with required parameters (server expects these even for audio reception only)
            pickup_future = asyncio.Future()

            # Use already generated pickup parameters (generated before SDP creation)
            ssrc = pickup_ssrc
            cname = pickup_cname
            
            logging.info(f"📊 Using pre-generated pickup parameters: SSRC={ssrc}, CNAME={cname}")

            # Get send transport info from server_info
            send_transport_info = server_info["sendTransport"]

            pickup_data = {
                "rtpCapabilities": RTP_CAPABILITIES,
                # CRITICAL FIX: Remove transportId - mobile app doesn't send it!
                # Including transportId confuses server and causes sendonly response
                "parameters": {
                    "kind": "audio",
                    "appData": {},
                    "rtpParameters": {
                        "mid": "0",
                        "encodings": [{"ssrc": ssrc, "dtx": False}],
                        "codecs": [{
                            "mimeType": "audio/PCMA",
                            "payloadType": 8,
                            "clockRate": 8000,
                            "channels": 1,
                            "parameters": {},
                            "rtcpFeedback": []
                        }],
                        "rtcp": {"reducedSize": True, "cname": cname},
                        "headerExtensions": [
                            {"parameters": {}, "id": 4, "uri": "urn:ietf:params:rtp-hdrext:sdes:mid", "encrypt": False},
                            {"parameters": {}, "id": 2, "uri": "http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time", "encrypt": False},
                            # CRITICAL FIX: RESTORE transport-cc extension to match mobile app exactly
                            # Mobile app INCLUDES transport-cc in pickup parameters but excludes from RTP capabilities
                            {"id": 3, "uri": "http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01", "encrypt": False, "parameters": {}},
                            {"parameters": {}, "uri": "urn:ietf:params:rtp-hdrext:ssrc-audio-level", "encrypt": False, "id": 1}
                        ]
                    }
                }
            }
            logging.debug(f"Sending pickup event")

            def pickup_callback(data):
                logging.debug(f"Received pickup response")
                pickup_future.set_result(data)

            await sio.emit("pickup", pickup_data, callback=pickup_callback)

            # Wait for pickup response
            pickup_response = await pickup_future
            if pickup_response and "result" in pickup_response:
                logging.info(f"Pickup successful: {pickup_response}")

                # Handle pickup response - both consumer (for audio reception) and producer (for audio transmission)
                pickup_result = pickup_response["result"]

                # Handle consumer creation (for receiving audio from doorbell)
                pickup_consumer_params = None
                if "consumer" in pickup_result:
                    consumer_info = pickup_result["consumer"]
                    logging.info(f"Audio consumer created from pickup: {consumer_info['id']}")
                    pickup_consumer_params = {"result": consumer_info}

                # Handle producer creation (for sending audio to doorbell)
                producer_id = None
                if "producerId" in pickup_result:
                    producer_id = pickup_result["producerId"]
                    logging.info(f"🎵 Audio producer created from pickup: {producer_id}")
                    logging.info("✅ Send transport was connected before pickup - producer created on connected transport!")

                    # CRITICAL: Connect our FileAudioTrack to the existing producer
                    await connect_audio_track_to_producer(producer_id)

                # Setup audio reception transport
                await setup_audio_transport_post_pickup(server_info, pickup_consumer_params)
            else:
                logging.warning(f"Pickup failed: {pickup_response}")
                call_failed_event.set()
            
        except Exception as e:
            logging.error(f"Error during pickup phase: {e}", exc_info=True)
            call_failed_event.set()

    async def setup_audio_transport_post_pickup(server_info, pickup_consumer_params=None):
        """Phase 3: Setup dedicated audio transport AFTER pickup (following network capture sequence)"""
        try:
            logging.info("Phase 3: Setting up dedicated audio transport after pickup...")
            
            # Get audio transport information from server_info
            audio_transport_info = server_info["recvTransportAudio"]
            audio_producer_id = server_info["producerIdAudio"]
            
            # CRITICAL: We MUST call transport_consume for audio on recvTransportAudio 
            # The pickup consumer (mid=0) and transport consumer (mid=1) are DIFFERENT
            # Network capture shows both are needed for proper bidirectional audio
            logging.info("Step 1: Calling transport_consume for audio on recvTransportAudio (required by network capture)")
            audio_consume_future = asyncio.Future()
            audio_transport_consume_data = {
                "transportId": audio_transport_info["id"],  # recvTransportAudio
                "producerId": audio_producer_id, 
                "rtpCapabilities": RTP_CAPABILITIES
            }
            logging.debug(f"Sending transport_consume for audio")
            
            def audio_consume_callback(data):
                logging.debug(f"Received audio transport_consume response")
                audio_consume_future.set_result(data)
            
            await sio.emit("transport_consume", audio_transport_consume_data, callback=audio_consume_callback)
            audio_consumer_params = await audio_consume_future
            logging.info(f"Audio transport consume response: {audio_consumer_params}")
            
            # Log both consumers for comparison
            if pickup_consumer_params:
                pickup_consumer_mid = pickup_consumer_params["result"]["rtpParameters"].get("mid", "unknown")
                logging.info(f"Pickup consumer: mid={pickup_consumer_mid} (pickup event response)")
            
            transport_consumer_mid = audio_consumer_params["result"]["rtpParameters"].get("mid", "unknown")
            logging.info(f"Transport consumer: mid={transport_consumer_mid} (transport_consume response)")
            logging.info("Both consumers are needed: pickup for event response, transport for actual audio track")
            
            # 2. Connect the dedicated audio receive transport (as shown in capture file [17])
            logging.info("Step 2: Connecting dedicated audio receive transport...")
            
            # For audio receive transport, use role: client (same as video receive transport)
            # CRITICAL: Use separate audio peer connection for audio transport
            # This matches the MediaSoup architecture where audio and video use separate transports
            logging.info("Step 2a: Setting up separate audio peer connection...")
            # Apply optimized ICE configuration for faster connection establishment
            optimized_config = create_optimized_rtc_configuration(server_info["iceServers"])
            audio_pc.configuration = optimized_config
            logging.info(f"Applied optimized ICE configuration to audio PC with {len(optimized_config.iceServers)} ICE servers")
            
            # CRITICAL: Use transport consumer parameters for SDP, not pickup consumer
            # The transport consumer (mid=1) is what creates the actual audio track
            # The pickup consumer (mid=0) is just for the pickup event response
            audio_rtp = audio_consumer_params["result"]["rtpParameters"]
            audio_payload_type = audio_rtp['codecs'][0]['payloadType']
            audio_mid = audio_rtp.get('mid', '1')  # Transport consumer uses mid=1
            logging.info(f"Using transport consumer parameters for SDP: mid={audio_mid}, ssrc={audio_rtp['encodings'][0]['ssrc']}")
            
            if pickup_consumer_params:
                pickup_rtp = pickup_consumer_params["result"]["rtpParameters"]
                logging.info(f"Pickup consumer was: mid={pickup_rtp.get('mid', '0')}, ssrc={pickup_rtp['encodings'][0]['ssrc']}")
                logging.info("Note: Pickup consumer is for pickup event only, transport consumer creates the actual track")
            
            # Get audio transport info
            audio_ice = audio_transport_info["iceParameters"]
            audio_dtls = audio_transport_info["dtlsParameters"]
            
            # Build audio-only SDP
            # CRITICAL FIX: Use header extensions from transport consumer, not pickup consumer
            # Mobile app uses different header extension IDs for transport consumer vs pickup event
            transport_header_exts = audio_rtp["headerExtensions"]
            logging.info(f"Using transport consumer header extensions: {transport_header_exts}")
            if pickup_consumer_params and "headerExtensions" in pickup_consumer_params["result"]["rtpParameters"]:
                pickup_header_exts = pickup_consumer_params["result"]["rtpParameters"]["headerExtensions"]
                logging.info(f"Pickup consumer had different header extensions: {pickup_header_exts}")
            
            audio_sdp_str = (f"v=0\r\no=- 5133548076695286524 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=msid-semantic: WMS\r\n"
                           f"a=ice-ufrag:{audio_ice['usernameFragment']}\r\na=ice-pwd:{audio_ice['password']}\r\n" +
                           "".join([f"a=fingerprint:{fp['algorithm']} {fp['value'].upper()}\r\n" for fp in audio_dtls['fingerprints']]) +
                           f"a=setup:actpass\r\n" +
                           f"m=audio 9 UDP/TLS/RTP/SAVPF {audio_payload_type}\r\nc=IN IP4 0.0.0.0\r\na=rtcp-mux\r\n" +
                           "".join([f"a=candidate:{c['foundation']} 1 {c['protocol']} {c['priority']} {c['ip']} {c['port']} typ {c['type']}\r\n" for c in audio_transport_info.get("iceCandidates", [])]) +
                           f"a=end-of-candidates\r\na=mid:{audio_mid}\r\na=sendrecv\r\n" +
                           "".join([f"a=extmap:{ext['id']} {ext['uri']}\r\n" for ext in transport_header_exts]) +
                           f"a=rtpmap:{audio_payload_type} PCMA/8000\r\n"
                           f"a=ssrc:{audio_rtp['encodings'][0]['ssrc']} cname:{audio_rtp['rtcp']['cname']}\r\n")
            
            logging.info(f"Audio SDP:\n{audio_sdp_str}")
            
            # Set remote description for audio peer connection
            audio_remote_description = RTCSessionDescription(sdp=audio_sdp_str, type="offer")
            await audio_pc.setRemoteDescription(audio_remote_description)
            
            # CRITICAL: After setting remote description, aiortc should automatically create transceivers
            # Let's verify this happened before proceeding
            audio_transceivers = audio_pc.getTransceivers()
            logging.debug(f"Audio transceivers after setRemoteDescription: {len(audio_transceivers)}")
            for i, transceiver in enumerate(audio_transceivers):
                logging.debug(f"Audio transceiver {i}: kind={transceiver.kind}, mid={transceiver.mid}, direction={transceiver.direction}")
            
            # Create and set local description for audio peer connection
            audio_answer = await audio_pc.createAnswer()
            await audio_pc.setLocalDescription(audio_answer)
            
            # Verify transceivers again after answer creation
            audio_transceivers = audio_pc.getTransceivers()
            logging.debug(f"Audio transceivers after createAnswer: {len(audio_transceivers)}")
            for i, transceiver in enumerate(audio_transceivers):
                logging.debug(f"Audio transceiver {i}: kind={transceiver.kind}, mid={transceiver.mid}, direction={transceiver.direction}")
                # With sendrecv direction, we should have both sender and receiver
                if transceiver.receiver:
                    logging.debug(f"Audio transceiver {i} has receiver, readyState={transceiver.receiver.track.readyState if transceiver.receiver.track else 'No track'}")
                else:
                    logging.debug(f"Audio transceiver {i} has no receiver yet")
                if transceiver.sender:
                    logging.debug(f"Audio transceiver {i} has sender, track={transceiver.sender.track is not None}")
                else:
                    logging.debug(f"Audio transceiver {i} has no sender yet")
            
            # Get audio DTLS parameters from the audio peer connection
            audio_transceivers = audio_pc.getTransceivers()
            if not audio_transceivers:
                raise Exception("No audio transceivers found")
            
            audio_dtls_transport = audio_transceivers[0].receiver.transport
            all_fingerprints = audio_dtls_transport.getLocalParameters().fingerprints
            
            # CRITICAL: Mobile app only sends sha-256 fingerprint for audio transport
            # Sending multiple fingerprints causes server to reject the connection
            sha256_fingerprint = None
            for fp in all_fingerprints:
                if fp.algorithm == "sha-256":
                    sha256_fingerprint = fp
                    break
            
            if not sha256_fingerprint:
                raise Exception("No sha-256 fingerprint found for audio transport")
                
            audio_dtls_params = {
                "role": "client", 
                "fingerprints": [
                    {"algorithm": "sha-256", "value": sha256_fingerprint.value}
                ]
            }
            
            logging.info(f"Audio receive transport DTLS params: {audio_dtls_params}")
            audio_transport_connect_data = {
                "transportId": audio_transport_info["id"], 
                "dtlsParameters": audio_dtls_params
            }
            logging.debug(f"Connecting audio transport")
            await sio.emit("transport_connect", audio_transport_connect_data)
            logging.info("Audio receive transport connect sent")
              
            # Add connection state monitoring to understand when tracks are created
            @audio_pc.on("connectionstatechange")
            async def on_audio_connection_state_change():
                logging.debug(f"Audio PC connection state changed to: {audio_pc.connectionState}")
                if audio_pc.connectionState == "connected":
                    logging.debug("Audio PC is now connected - checking for tracks...")
                    # Check for tracks immediately when connection is established
                    for i, transceiver in enumerate(audio_pc.getTransceivers()):
                        if transceiver.receiver and transceiver.receiver.track:
                            track = transceiver.receiver.track
                            logging.debug(f"Found track in connected audio PC transceiver {i}: kind={track.kind}, id={track.id}")
                            if track.kind == "audio" and audio_track is None:
                                logging.debug("Found audio track after connection - triggering event!")
                                await on_audio_track(track)
            
            @audio_pc.on("icecandidatepairchange")
            async def on_audio_ice_candidate_pair_change():
                logging.debug(f"Audio PC ICE candidate pair changed, state: {audio_pc.iceConnectionState}")
            
            # Wait for transport to connect before proceeding
            await asyncio.sleep(0.5)
            
            # 3. Now that separate audio transport is connected, audio track should be created automatically
            # Don't wait indefinitely - the track should appear within a few seconds
            logging.info("Step 3: Waiting for audio transport to establish and audio track to be created...")
            
            # Immediately check if audio tracks were created after transport connect
            audio_transceivers = audio_pc.getTransceivers()
            logging.debug(f"Checking for audio tracks immediately after transport connect...")
            for i, transceiver in enumerate(audio_transceivers):
                if transceiver.receiver and transceiver.receiver.track:
                    track = transceiver.receiver.track
                    logging.debug(f"Found track immediately after transport connect: kind={track.kind}, id={track.id}")
                    if track.kind == "audio" and audio_track is None:
                        logging.debug("Found audio track immediately after transport connect!")
                        await on_audio_track(track)
            
            # Debug: Check current transceivers and their status for both peer connections
            transceivers = pc.getTransceivers()
            audio_transceivers = audio_pc.getTransceivers()
            logging.debug(f"Video PC transceivers: {len(transceivers)}, Audio PC transceivers: {len(audio_transceivers)}")
            
            for i, transceiver in enumerate(transceivers):
                logging.debug(f"Video transceiver {i}: kind={transceiver.kind}, direction={transceiver.direction}, mid={transceiver.mid}")
                if transceiver.receiver and transceiver.receiver.track:
                    logging.debug(f"Video transceiver {i} has track: {transceiver.receiver.track.kind}")
            
            for i, transceiver in enumerate(audio_transceivers):
                logging.debug(f"Audio transceiver {i}: kind={transceiver.kind}, direction={transceiver.direction}, mid={transceiver.mid}")
                # Now expecting mid=1 for transport consumer, and sendrecv direction
                if transceiver.receiver and transceiver.receiver.track:
                    logging.debug(f"Audio transceiver {i} has track: {transceiver.receiver.track.kind}")
            
            # Wait longer and check periodically for audio track creation in the audio peer connection
            for wait_round in range(10):  # Wait up to 10 seconds
                await asyncio.sleep(1)
                if audio_track is not None:
                    logging.debug(f"Audio track created after {wait_round + 1} seconds!")
                    break
                    
                # Check if any new transceivers appeared in the audio peer connection
                current_audio_transceivers = audio_pc.getTransceivers()
                if len(current_audio_transceivers) > len(audio_transceivers):
                    logging.debug(f"New audio transceivers appeared: {len(current_audio_transceivers)} (was {len(audio_transceivers)})")
                    audio_transceivers = current_audio_transceivers
                
                # Check for audio tracks in the audio peer connection
                for i, transceiver in enumerate(audio_transceivers):
                    logging.debug(f"Audio transceiver {i} details: mid={transceiver.mid}, direction={transceiver.direction}")
                    # Now expecting mid=1 for the transport consumer
                    if transceiver.receiver:
                        logging.debug(f"Audio transceiver {i} has receiver")
                        if transceiver.receiver.track:
                            track = transceiver.receiver.track
                            logging.debug(f"Found track in audio transceiver {i}: kind={track.kind}, id={track.id}")
                            if track.kind == "audio" and audio_track is None:
                                logging.debug("Found audio track in audio PC that wasn't detected by on_track event!")
                                # Manually trigger the track event
                                await on_audio_track(track)
                                break
                        else:
                            logging.debug(f"Audio transceiver {i} receiver has no track yet")
                    else:
                        logging.debug(f"Audio transceiver {i} has no receiver")
                
                logging.debug(f"Still waiting for audio track... ({wait_round + 1}/10)")
            
            if audio_track is None:
                logging.debug("Audio track still not created after 10 seconds. Checking peer connection states.")
                logging.debug(f"Video PC connection state: {pc.connectionState}")
                logging.debug(f"Video PC ICE connection state: {pc.iceConnectionState}")
                logging.debug(f"Video PC signaling state: {pc.signalingState}")
                logging.debug(f"Audio PC connection state: {audio_pc.connectionState}")
                logging.debug(f"Audio PC ICE connection state: {audio_pc.iceConnectionState}")
                logging.debug(f"Audio PC signaling state: {audio_pc.signalingState}")
            
            # 4. Start track processing now that transports should be connected
            logging.debug(f"About to start track processing. video_track={video_track is not None}, audio_track={audio_track is not None}")
            
            # Track processing now starts immediately when tracks are received via on_track handlers
            # Video starts as soon as video track is available, audio starts when audio track is available
            logging.info("Track processing will start automatically when tracks are received")

            logging.info("Phase 3 complete: Audio transport setup finished!")
            
        except Exception as e:
            logging.error(f"Error during post-pickup audio transport setup: {e}", exc_info=True)
            call_failed_event.set()

    try:
        # CRITICAL FIX: Set User-Agent to match mobile app
        # Server might check User-Agent to enable audio transmission features
        headers = {
            'User-Agent': 'Blue/6 CFNetwork/3826.500.131 Darwin/24.5.0'
        }

        await sio.connect(config.SERVER_URL,
                         headers=headers,
                         transports=["websocket", "polling"], socketio_path="/socket.io")
        await call_failed_event.wait()
    except asyncio.CancelledError:
        logging.info("Two-way audio doorbell session cancelled by user.")
        raise
    finally:
        # Cancel track processing tasks
        for task in track_tasks:
            if not task.done():
                task.cancel()

        # Wait for tasks to finish cancellation
        if track_tasks:
            await asyncio.gather(*track_tasks, return_exceptions=True)

        logging.info("Clearing live flags - switching back to dummy content")
        config.is_live.clear()
        config.is_audio_live.clear()

        # Send end signals to both video and audio queues
        try:
            await config.live_frame_queue.put(None)
        except Exception as e:
            logging.warning(f"Failed to send video end signal: {e}")

        try:
            await config.live_audio_queue.put(None)
        except Exception as e:
            logging.warning(f"Failed to send audio end signal: {e}")

        # Let FFmpeg continue running - stream generators will switch back to dummy content
        logging.info("WebRTC session ended - stream generators will revert to dummy content")

        # Send hang_up event before disconnecting (following network capture protocol)
        if sio and sio.connected:
            try:
                logging.info("Sending hang_up event to server")
                hang_up_data = {}
                logging.debug(f"Sending hang_up event")
                await sio.emit("hang_up", hang_up_data)
                # Wait briefly for server acknowledgment
                await asyncio.sleep(0.5)
            except Exception as e:
                logging.warning(f"Failed to send hang_up event: {e}")

            await sio.disconnect()
        if pc and pc.connectionState != 'closed':
            await pc.close()
        if audio_pc and audio_pc.connectionState != 'closed':
            await audio_pc.close()
        if send_pc and send_pc.connectionState != 'closed':
            await send_pc.close()

        # Stop audio track if it exists
        if send_audio_track:
            try:
                send_audio_track.stop()
            except Exception as e:
                logging.warning(f"Error stopping send audio track: {e}")

        # Force garbage collection after WebRTC cleanup to prevent memory leaks
        import gc
        collected = gc.collect()
        logging.info(f"WebRTC session cleanup complete. GC collected {collected} objects.")

        # Check memory usage after session cleanup
        try:
            import psutil
            import os
            process = psutil.Process(os.getpid())
            memory_mb = process.memory_info().rss / 1024 / 1024
            logging.info(f"Memory usage after WebRTC cleanup: {memory_mb:.1f}MB")
        except Exception as e:
            logging.warning(f"Could not check memory usage after cleanup: {e}")